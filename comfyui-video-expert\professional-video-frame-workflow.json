{"last_node_id": 25, "last_link_id": 40, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [100, 100], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2, 3], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [4, 5], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["Realistic\\realisticVisionV60_v60B1VAE.safetensors"]}, {"id": 2, "type": "LoadImage", "pos": [100, 250], "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6, 7, 8], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["model_reference.jpg", "image"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [500, 100], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [9, 10], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["(masterpiece, best quality), beautiful woman, from confident standing pose to gentle sitting position, expression changing from serious to warm smile, professional photography, cinematic lighting, detailed facial features, consistent character, 5 second motion"]}, {"id": 4, "type": "CLIPTextEncode", "pos": [500, 350], "size": {"0": 400, "1": 200}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11, 12], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, deformed, bad anatomy, bad hands, missing fingers, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed face, ugly, bad proportions, extra limbs, inconsistent character, face change, different person"]}, {"id": 5, "type": "IPAdapterModelLoader", "pos": [100, 600], "size": {"0": 315, "1": 58}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IPADAPTER", "type": "IPADAPTER", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterModelLoader"}, "widgets_values": ["ip-adapter-faceid-plus_sd15.bin"]}, {"id": 6, "type": "CLIPVisionLoader", "pos": [100, 700], "size": {"0": 315, "1": 58}, "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPVisionLoader"}, "widgets_values": ["CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors"]}, {"id": 7, "type": "IPAdapterFaceID", "pos": [500, 600], "size": {"0": 315, "1": 258}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "ipadapter", "type": "IPADAPTER", "link": 13}, {"name": "image", "type": "IMAGE", "link": 6}, {"name": "clip_vision", "type": "CLIP_VISION", "link": 14}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterFaceID"}, "widgets_values": [0.85, 0.8, "linear", "concat", 0.0, 1.0, "V only", false, false, false]}, {"id": 8, "type": "ControlNetLoader", "pos": [900, 100], "size": {"0": 315, "1": 58}, "flags": {}, "order": 7, "mode": 0, "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["control_v11p_sd15_openpose.pth"]}, {"id": 9, "type": "OpenposePreprocessor", "pos": [900, 200], "size": {"0": 315, "1": 106}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [17, 18], "slot_index": 0}], "properties": {"Node name for S&R": "OpenposePreprocessor"}, "widgets_values": ["enable", "enable", "enable", 512]}, {"id": 10, "type": "ControlNetApply", "pos": [900, 350], "size": {"0": 315, "1": 98}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 9}, {"name": "control_net", "type": "CONTROL_NET", "link": 16}, {"name": "image", "type": "IMAGE", "link": 17}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [19], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetApply"}, "widgets_values": [0.8, 0.0, 0.8]}, {"id": 11, "type": "ControlNetApply", "pos": [900, 500], "size": {"0": 315, "1": 98}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 10}, {"name": "control_net", "type": "CONTROL_NET", "link": 16}, {"name": "image", "type": "IMAGE", "link": 18}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [20], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetApply"}, "widgets_values": [0.6, 0.2, 1.0]}, {"id": 12, "type": "AnimateDiff<PERSON><PERSON>der", "pos": [1300, 100], "size": {"0": 315, "1": 58}, "flags": {}, "order": 11, "mode": 0, "outputs": [{"name": "MOTION_MODEL", "type": "MOTION_MODEL", "links": [21], "slot_index": 0}], "properties": {"Node name for S&R": "AnimateDiff<PERSON><PERSON>der"}, "widgets_values": ["mm_sd_v15_v2.ckpt"]}, {"id": 13, "type": "Animate<PERSON>iffC<PERSON>ine", "pos": [1300, 200], "size": {"0": 315, "1": 98}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 15}, {"name": "motion_model", "type": "MOTION_MODEL", "link": 21}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [22, 23], "slot_index": 0}], "properties": {"Node name for S&R": "Animate<PERSON>iffC<PERSON>ine"}, "widgets_values": [0.7, true]}, {"id": 14, "type": "EmptyLatentImage", "pos": [1300, 350], "size": {"0": 315, "1": 106}, "flags": {}, "order": 13, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [24, 25], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 768, 1]}, {"id": 15, "type": "K<PERSON><PERSON><PERSON>", "pos": [1700, 100], "size": {"0": 315, "1": 262}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 22}, {"name": "positive", "type": "CONDITIONING", "link": 19}, {"name": "negative", "type": "CONDITIONING", "link": 11}, {"name": "latent_image", "type": "LATENT", "link": 24}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [26], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [123456, "fixed", 25, 8.0, "dpmpp_2m", "karras", 0.3]}, {"id": 16, "type": "K<PERSON><PERSON><PERSON>", "pos": [1700, 400], "size": {"0": 315, "1": 262}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 23}, {"name": "positive", "type": "CONDITIONING", "link": 20}, {"name": "negative", "type": "CONDITIONING", "link": 12}, {"name": "latent_image", "type": "LATENT", "link": 25}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [27], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [234567, "fixed", 20, 7.0, "euler", "normal", 0.8]}, {"id": 17, "type": "VAEDecode", "pos": [2100, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 26}, {"name": "vae", "type": "VAE", "link": 4}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 18, "type": "VAEDecode", "pos": [2100, 400], "size": {"0": 210, "1": 46}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 27}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [29], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 19, "type": "SaveImage", "pos": [2400, 100], "size": {"0": 315, "1": 58}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 28}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["video_frame_start"]}, {"id": 20, "type": "SaveImage", "pos": [2400, 400], "size": {"0": 315, "1": 58}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 29}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["video_frame_end"]}], "links": [[1, 1, 0, 7, 0, "MODEL"], [2, 1, 1, 3, 0, "CLIP"], [3, 1, 1, 4, 0, "CLIP"], [4, 1, 2, 17, 1, "VAE"], [5, 1, 2, 18, 1, "VAE"], [6, 2, 0, 7, 2, "IMAGE"], [7, 2, 0, 9, 0, "IMAGE"], [8, 2, 0, 9, 0, "IMAGE"], [9, 3, 0, 10, 0, "CONDITIONING"], [10, 3, 0, 11, 0, "CONDITIONING"], [11, 4, 0, 15, 2, "CONDITIONING"], [12, 4, 0, 16, 2, "CONDITIONING"], [13, 5, 0, 7, 1, "IPADAPTER"], [14, 6, 0, 7, 3, "CLIP_VISION"], [15, 7, 0, 13, 0, "MODEL"], [16, 8, 0, 10, 1, "CONTROL_NET"], [16, 8, 0, 11, 1, "CONTROL_NET"], [17, 9, 0, 10, 2, "IMAGE"], [18, 9, 0, 11, 2, "IMAGE"], [19, 10, 0, 15, 1, "CONDITIONING"], [20, 11, 0, 16, 1, "CONDITIONING"], [21, 12, 0, 13, 1, "MOTION_MODEL"], [22, 13, 0, 15, 0, "MODEL"], [23, 13, 0, 16, 0, "MODEL"], [24, 14, 0, 15, 3, "LATENT"], [25, 14, 0, 16, 3, "LATENT"], [26, 15, 0, 17, 0, "LATENT"], [27, 16, 0, 18, 0, "LATENT"], [28, 17, 0, 19, 0, "IMAGE"], [29, 18, 0, 20, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}